import { useState, useEffect, use } from "react";
import CardList from "./components/card-list/card-list.component";
import "./App.css";

const App = () => {
  const [monsters, setMonsters] = useState([]);
  const [filteredMonsters, setFilteredMonsters] = useState([]);

  useEffect(() => {
    fetch("https://jsonplaceholder.typicode.com/users").then((res) => {
      res.json().then((data) => {
        setMonsters(data);
      });
    });
  }, []);

  return (
    <>
      <input
        type="search"
        onchange={() => {
          const searchString = event.target.value.toLowerCase();
          const newFilteredMonsters = monsters.filter((monster) => {
            return monster.name.toLowerCase().includes(searchString);
          });
          setFilteredMonsters(newFilteredMonsters);
        }}
      />
      <CardList monsters={filteredMonsters} />
    </>
  );
};

export default App;
